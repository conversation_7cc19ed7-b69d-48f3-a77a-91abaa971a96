export enum ConnectionStatus {
  CONNECTION_OK = 1,
  CONNECTION_LOST = 2,
  CONNECTION_ERROR = 5,
  MESSAGE = 3,
  RELOGIN = 4
}

export enum LoginState {
  UNKNOWN = '',
  LOGOUT = 'LOGOUT',
  LOGINING = 'LOGINING',
  LOGIN_SUCCESS = 'LOGIN_SUCCESS',
  LOGIN_FAILED = ' LOGIN_FAILED'
}

export const REMOTE_CONTROL = {
  REMOTE_REQUEST_REMOTE_MOBILE_CONTROL: 'REMOTE_REQUEST_REMOTE_MOBILE_CONTROL',
  REMOTE_REQUEST_RESET_ABNORMAL_CONTROL: 'REMOTE_REQUEST_RESET_ABNORMAL_CONTROL'
};
export const COMMAND_TYPE = {
  MOBILE: 'MOBILE',
  SUPER: 'SUPER' // 强制遥控
};
export enum ToolKey {
  ReportSoftware = 'ReportSoftware',
  ReportHardware = 'ReportHardware'
}
export enum SystemStatus {
  NORMAL = 'NORMAL',
  ABNORMAL = 'ABNORMAL',
  OFFLINE = 'OFFLINE'
}
export enum ScheduleState {
  WAITING = 'WAITING', //  "无任务"
  ALREADY = 'ALREADY', // "待出发"
  SETOUT = 'SETOUT', // "去程"
  DELIVERY = 'DELIVERY', //  "任务中"
  RETURN = 'RETURN', //   "返程"
  ARRIVED = 'ARRIVED', // "已到达"
  TOLOAD = 'TOLOAD', // "去装载"
  LOADING = 'LOADING', // "装载中"
  TOUNLOAD = 'TOUNLOAD', // "去卸载"
  UNLOADING = 'UNLOADING' // "卸载中"
}

export enum StopTravelStatus {
  INIT = 'INIT',
  START = 'START',
  ARRIVED = 'ARRIVED',
  DEPART = 'DEPART',
  SKIP = 'SKIP',
  STAY = 'STAY'
}

export enum StopAction {
  START = 'START',
  LOAD = 'LOAD',
  PICKUP = 'PICKUP',
  UNLOAD = 'UNLOAD',
  VEND = 'VEND',
  DROPOFF = 'DROPOFF',
  RETURN = 'RETURN'
}

export enum TakeOverType {
  TAKEOVER = 'TAKEOVER',
  TEMPORARY = 'TEMPORARY',
  NONE = 'NONE'
}

export const TakeOverTypeName = {
  TAKEOVER: '接管',
  TEMPORARY: '临时接管'
};
export const TakeOverTypeNameMap = new Map([
  ['TAKEOVER', '接管'],
  ['TEMPORARY', '临时接管']
]);

export enum TakeOverSourceType {
  MONITOR = 'MONITOR',
  MINI_MONITOR = 'MINI_MONITOR',
  REMOTE_JOYSTICK = 'REMOTE_JOYSTICK',
  WORKBENCH_MONITOR = 'WORKBENCH_MONITOR'
}

export const TakeOverSourceTypeName = {
  [TakeOverSourceType.MONITOR]: '监控',
  [TakeOverSourceType.MINI_MONITOR]: '小程序',
  [TakeOverSourceType.REMOTE_JOYSTICK]: '驾舱',
  [TakeOverSourceType.WORKBENCH_MONITOR]: '小哥工作台'
};

export const TakeOverSourceNameMap = new Map([
  [TakeOverSourceType.MONITOR, '监控'],
  [TakeOverSourceType.MINI_MONITOR, '小程序'],
  [TakeOverSourceType.REMOTE_JOYSTICK, '驾舱'],
  [TakeOverSourceType.WORKBENCH_MONITOR, '小哥工作台']
]);
export enum CommandType {
  REMOTE_POWER_ON = 'REMOTE_POWER_ON',
  REMOTE_SHUTDOWN = 'REMOTE_SHUTDOWN',
  ROVER_REBOOT = 'ROVER_REBOOT',
  VEHICLE_REBOOT = 'VEHICLE_REBOOT',
  ANDROID_REBOOT = 'ANDROID_REBOOT',
  VIDEO_REBOOT = 'VIDEO_REBOOT',
  MCU_REBOOT = 'MCU_REBOOT'
}
export const CommandNameMap = new Map([
  [CommandType.REMOTE_SHUTDOWN, '远程关机'],
  [CommandType.VIDEO_REBOOT, '视频重启'],
  [CommandType.MCU_REBOOT, '域控重启'],
  [CommandType.ROVER_REBOOT, 'Rover重启'],
  [CommandType.VEHICLE_REBOOT, '断电重启'],
  [CommandType.ANDROID_REBOOT, '安卓重启'],
  [CommandType.REMOTE_POWER_ON, '远程开机']
]);
export const CommandDialogMap = new Map([
  [CommandType.REMOTE_SHUTDOWN, '请确保车辆已停车，确定关机吗？'],
  [CommandType.VIDEO_REBOOT, '请确保车辆已停车，确定视频重启吗？'],
  [CommandType.MCU_REBOOT, '请确保车辆已停车，确定域控重启吗？'],
  [CommandType.ROVER_REBOOT, '请确保车辆已停车，确定rover重启吗？'],
  [CommandType.VEHICLE_REBOOT, '请确保车辆已停车，确定断电重启吗？'],
  [CommandType.ANDROID_REBOOT, '请确保车辆已停车，确定安卓重启吗？'],
  [CommandType.REMOTE_POWER_ON, '确认开机吗？']
]);

export enum AbnormalVehicleType {
  CONNECTION_LOST = 'CONNECTION_LOST',
  VEHICLE_CRASH = 'VEHICLE_CRASH',
  MANUAL_REPORT = 'MANUAL_REPORT',
  SENSOR_ERROR = 'SENSOR_ERROR',
  OPERATION_ALARM = 'OPERATION_ALARM',
  BOOT_ALARM = 'BOOT_ALARM',
  INTERSECTION_ABNORMAL = 'INTERSECTION_ABNORMAL',
  STOP = 'STOP',
  CLOUD_WARN = 'CLOUD_WARN',
  LOW_BATTERY = 'LOW_BATTERY',
  LOW_PRESSURE = 'LOW_PRESSURE'
}

export enum TodoListType {
  ACCIDENT = 'ACCIDENT',
  REPAIR = 'REPAIR'
}
export enum RepairType {
  REPAIR_REJECT = 'REPAIR_REJECT',
  REPAIR_CONFIRM = 'REREPAIR_CONFIRMPAIR'
}
export enum PermissionKey {
  remoteControlBtn = 'remoteControlBtn',
  callDriverBtn = 'callDriverBtn',
  openGridBtn = 'openGridBtn',
  freeDrivingBtn = 'freeDrivingBtn',
  restartBtn = 'restartBtn',
  followVehicleBtn = 'followVehicleBtn',
  findCarBtn = 'findCarBtn',
  callFrontBtn = 'callFrontBtn',
  loginVehicleBtn = 'loginVehicleBtn',
  trackMapBtn = 'trackMapBtn',
  reportSoftwareBtn = 'reportSoftwareBtn',
  reportHardwareBtn = 'reportHardwareBtn',
  realtime = 'realtime',
  stopPointBtn = 'stopPointBtn',
  anyDriveBtn = 'anyDriveBtn',
  techMaintenanceBtn = 'techMaintenanceBtn',

  // TabBar权限
  notificationTab = 'bottomTab_notification',
  vehicleTab = 'bottomTab_vehicle',
  workbenchTab = 'bottomTab_workbench',
  myTab = 'bottomTab_my',

  // 我的页面的碰撞电话语音通知按钮
  collisionNoteBtn = 'collisionNoteBtn'
}

export const TodoListMenu = {
  All: { name: '全部', key: 'All' },
  [TodoListType.ACCIDENT]: { name: '事故', key: TodoListType.ACCIDENT },
  [TodoListType.REPAIR]: { name: '维修单', key: TodoListType.REPAIR }
};
export enum RepairOrderStatus {
  UNDER_REPAIR = 'UNDER_REPAIR', // 维修中
  PENDING_ACCEPTANCE = 'PENDING_ACCEPTANCE', // 待验收
  ACCEPTANCE_REJECTED = 'ACCEPTANCE_REJECTED', // 验收驳回
  COMPLETED = 'COMPLETED' // 已完成
}
export const RepairStatusMenu = {
  [RepairOrderStatus.UNDER_REPAIR]: {
    name: '维修中',
    key: RepairOrderStatus.UNDER_REPAIR
  },
  [RepairOrderStatus.PENDING_ACCEPTANCE]: {
    name: '待验收',
    key: RepairOrderStatus.PENDING_ACCEPTANCE
  },
  [RepairOrderStatus.ACCEPTANCE_REJECTED]: {
    name: '验收驳回',
    key: RepairOrderStatus.ACCEPTANCE_REJECTED
  },
  [RepairOrderStatus.COMPLETED]: {
    name: '已完成',
    key: RepairOrderStatus.COMPLETED
  }
};
export enum IsInfluenceOperation {
  INFLUENCE = 1, // 影响运营
  NO_INFLUENCE = 0 // 不影响运营
}
export enum IsAllowOEMVisible {
  ALLOW = 1, // 允许
  NOT_ALLOW = 0 // 不允许
}
export const AbnormalVehicleMenu = {
  All: { name: '全部', key: 'All' },
  [AbnormalVehicleType.VEHICLE_CRASH]: {
    name: '碰撞',
    key: AbnormalVehicleType.VEHICLE_CRASH
  },
  [AbnormalVehicleType.CLOUD_WARN]: {
    name: '云端预警',
    key: AbnormalVehicleType.CLOUD_WARN
  },
  [AbnormalVehicleType.MANUAL_REPORT]: {
    name: '人工告警',
    key: AbnormalVehicleType.MANUAL_REPORT
  },
  [AbnormalVehicleType.STOP]: { name: '停车', key: AbnormalVehicleType.STOP },
  [AbnormalVehicleType.INTERSECTION_ABNORMAL]: {
    name: '路口异常',
    key: AbnormalVehicleType.INTERSECTION_ABNORMAL
  },
  [AbnormalVehicleType.CONNECTION_LOST]: {
    name: '失联',
    key: AbnormalVehicleType.CONNECTION_LOST
  },
  [AbnormalVehicleType.SENSOR_ERROR]: {
    name: '传感器异常',
    key: AbnormalVehicleType.SENSOR_ERROR
  },
  [AbnormalVehicleType.BOOT_ALARM]: {
    name: '开机异常',
    key: AbnormalVehicleType.BOOT_ALARM
  },
  [AbnormalVehicleType.LOW_PRESSURE]: {
    name: '胎压异常',
    key: AbnormalVehicleType.LOW_PRESSURE
  },
  [AbnormalVehicleType.LOW_BATTERY]: {
    name: '低电量',
    key: AbnormalVehicleType.LOW_BATTERY
  }
};

export enum HandleMethod {
  QUICK_SETTLEMENT = 'QUICK_SETTLEMENT', // 快速处理(私了)
  POLICE_DETERMINATION_NO_INSURANCE = 'POLICE_DETERMINATION_NO_INSURANCE', // 需交警定责，无需保险报案
  POLICE_DETERMINATION_WITH_INSURANCE = 'POLICE_DETERMINATION_WITH_INSURANCE' // 需交警定责，需保险报案
}

export enum AccidentType {
  PASSIVE = 'PASSIVE_CRASH', // 被动碰撞
  ACTIVE = 'INITIATIVE_CRASH' // 主动碰撞
}

export enum AccidentJudge {
  SHARED_LIABILITY = 'SHARED_LIABILITY', // 多方责任
  FULL_LIABILITY = 'FULL_LIABILITY', // 无人车全责
  NO_LIABILITY = 'NO_LIABILITY' // 无人车无责
}

export const REMOTE_COMMAND = {
  FLASH_LIGHT_OPEN: 'FLASH_LIGHT_OPEN',
  FLASH_LIGHT_CLOSE: 'FLASH_LIGHT_CLOSE',
  LOW_LIGHT_OPEN: 'LOW_LIGHT_OPEN',
  LOW_LIGHT_CLOSE: 'LOW_LIGHT_CLOSE',
  LEFT_TURN_LIGHT_OPEN: 'LEFT_TURN_LIGHT_OPEN',
  LEFT_TURN_LIGHT_CLOSE: 'LEFT_TURN_LIGHT_CLOSE',
  RIGHT_TURN_LIGHT_OPEN: 'RIGHT_TURN_LIGHT_OPEN',
  RIGHT_TURN_LIGHT_CLOSE: 'RIGHT_TURN_LIGHT_CLOSE'
};

export const GestureState = {
  POSSIBLE: 0, // 0 此时手势未识别，如 panDown等
  BEGIN: 1, // 1 手势已识别
  ACTIVE: 2, // 2 连续手势活跃状态
  END: 3, // 3 手势终止
  CANCELLED: 4 // 4 手势取消，
};

export enum TMapResStatus {
  Success = 0,
  RequestError = 310,
  KeyFormatError = 311,
  Unauthorized = 110,
  WXInternalError = 1000
}

export enum WSReadyState {
  CONNECTING = 0,
  OPEN = 1,
  CLOSING = 2,
  CLOSED = 3
}
export const MapUpdaterStatusEnum = {
  TO_UPGRADE: {
    key: 0,
    name: '待生效'
  },
  TO_DOWNLOAD: {
    key: 1,
    name: '待下载'
  },
  DOWNLOADING: {
    key: 2,
    name: '下载中'
  },
  DOWNLOADED: {
    key: 3,
    name: '待升级'
  },
  UPGRADE: {
    key: 4,
    name: '升级中'
  },
  SUCCESS: {
    key: 5,
    name: '升级成功'
  },
  FAILURE: {
    key: 6,
    name: '升级失败'
  }
};

// 费用承担类型枚举
export enum CostType {
  JD = 'JD',
  OEM = 'OEM',
  SERVICE_STATION = 'SERVICE_STATION',
  SUPPLIER = 'SUPPLIER',
  INSURANCE = 'INSURANCE',
  OTHER = 'OTHER'
}

// 费用承担类型名称映射
export const CostTypeNameMap = {
  [CostType.JD]: '京东',
  [CostType.OEM]: '整车厂',
  [CostType.SERVICE_STATION]: '服务站',
  [CostType.SUPPLIER]: '供应商',
  [CostType.INSURANCE]: '保险公司',
  [CostType.OTHER]: '其他'
};

// 操作日志类型枚举
export enum OperationLogType {
  NEW = 'NEW',
  ACCEPTED = 'ACCEPTED',
  CAN_NOT_ACCEPT = 'CAN_NOT_ACCEPT',
  COMPLETE_REQUIRE = 'COMPLETE_REQUIRE',
  TEMPORARY_STORAGE_REQUIRE = 'TEMPORARY_STORAGE_REQUIRE',
  AFFIRM = 'AFFIRM',
  REJECTED = 'REJECTED'
}

// 操作日志类型名称映射
export const OperationLogTypeNameMap = {
  [OperationLogType.NEW]: '报修',
  [OperationLogType.ACCEPTED]: '受理',
  [OperationLogType.CAN_NOT_ACCEPT]: '不受理',
  [OperationLogType.COMPLETE_REQUIRE]: '跟进-维修完成',
  [OperationLogType.TEMPORARY_STORAGE_REQUIRE]: '跟进-维修暂存',
  [OperationLogType.AFFIRM]: '跟进-验收通过',
  [OperationLogType.REJECTED]: '跟进-验收不通过'
};

// TabBar配置
export const TAB_BAR_PAGES = [
  {
    key: 'notification',
    path: '/pages/notification/index',
    text: '消息',
    permission: PermissionKey.notificationTab,
    iconSelected: 'NotificationSelected',
    iconUnselected: 'NotificationUnselected',
    showBadge: true // 是否显示消息数量徽章
  },
  {
    key: 'vehicle',
    path: '/pages/vehicle/index',
    text: '车辆',
    permission: PermissionKey.vehicleTab,
    iconSelected: 'VehicleSelected',
    iconUnselected: 'VehicleUnselected'
  },
  {
    key: 'workbench',
    path: '/pages/workbench/index',
    text: '工作台',
    permission: PermissionKey.workbenchTab,
    iconSelected: 'WorkbenchSelected',
    iconUnselected: 'WorkbenchUnselected'
  },
  {
    key: 'my',
    path: '/pages/my/index',
    text: '我的',
    permission: PermissionKey.myTab,
    iconSelected: 'MySelected',
    iconUnselected: 'MyUnselected'
  }
];

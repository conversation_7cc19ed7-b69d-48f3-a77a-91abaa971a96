<template>
  <view class="enhanced-uploader {{layoutDirection}}">
    <scroll-view
      scroll-y="{{true}}"
      style="height: {{previewHeight}}px; transition: height 0.3s ease-in-out;"
    >
      <view wx:if="{{mediaFiles.length > 0}}">
        <preview-media
          media-list="{{mediaFiles}}"
          show-delete="{{true}}"
          show-upload-mask="{{true}}"
          bind:delete="handleDelete"
          total-width="{{650}}"
        />
      </view>

      <view wx:if="{{otherFiles.length > 0}}">
        <preview-files
          file-list="{{otherFiles}}"
          show-delete="{{true}}"
          show-upload-mask="{{true}}"
          bind:delete="handleDelete"
        />
      </view>
    </scroll-view>
    <view bindtap="handleUploadTap" class="upload-trigger-container">
      <slot name="upload-trigger" />
    </view>
  </view>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { ParallelUploader } from '../utils/paralleUploader';
  import { doRequest, HTTPSTATUSCODE } from '../api/fetch';
  import { getLOPDN } from '../utils/config';

  const wxfs = wx.getFileSystemManager();

  createComponent<any>({
    options: {
      multipleSlots: true // 启用多 slot 支持
    },
    properties: {
      layoutDirection: {
        type: String,
        value: 'column' // 'column' | 'row' | 'column-reverse'
      },
      // 最大并发数
      maxConcurrent: {
        type: Number,
        value: 2
      },
      // 最大文件数量
      maxFiles: {
        type: Number,
        value: 10
      },
      // 支持的文件类型
      acceptTypes: {
        type: Array,
        value: ['image', 'video', 'file']
      },
      // 文件大小限制(MB)
      maxFileSize: {
        type: Number,
        value: 50
      },
      perRowMediaCount: {
        type: Number,
        value: 4
      },
      previewHeight: {
        type: Number,
        value: 300
      }
    },
    data: {
      uploader: null,
      allFiles: [],
      uploadProgress: 0,
      uploadResult: null,
      isUploading: false,
      // 缓存计算结果
      _cachedMediaFiles: [],
      _cachedOtherFiles: [],
      _lastFilesHash: '',
      // 新增
      mediaFiles: [],
      otherFiles: [],
      showUploadMedia: true,
      showUploadFile: true
    },

    watch: {
      allFiles: function (newFiles: any[]) {
        // 只有当文件列表真正变化时才重新计算
        const newHash = JSON.stringify(newFiles.map((f: any) => f.name + f.type));
        if (newHash !== this.data._lastFilesHash) {
          this.updateCachedFiles(newFiles);
          this.updateComputedData();
          this.setData({ _lastFilesHash: newHash });
        }
      },
      isUploading: {
        handler: function (newVal: boolean) {
          // 当上传状态变化时，更新计算数据
          console.log('isUploading上传状态变化:', newVal);
          this.updateComputedData({ isUploading: newVal });
        },
        immediate: true
      }
    },

    lifetimes: {
      attached() {
        this.initUploader();
        this.throttledUpload = this.throttle(this.handleUploadTap, 1000);
        this.updateComputedData();
      },

      detached() {
        // 清理资源
        if (this.data.uploader) {
          this.data.uploader = null;
        }
      }
    },

    methods: {
      // 更新缓存的文件列表
      updateCachedFiles(files: any[]) {
        const mediaFiles = files.filter(
          (file: any) => file.type === 'image' || file.type === 'video'
        );
        const otherFiles = files.filter(
          (file: any) => file.type !== 'image' && file.type !== 'video'
        );

        this.setData({
          _cachedMediaFiles: mediaFiles,
          _cachedOtherFiles: otherFiles
        });
      },

      initUploader() {
        const uploader = new ParallelUploader(
          this.data.maxConcurrent,
          this.uploadFile.bind(this)
        );
        this.setData({ uploader });
      },

      throttle(func: Function, wait: number) {
        let timeout: NodeJS.Timeout | null = null;
        const self = this;
        return function (...args: any[]) {
          if (!timeout) {
            timeout = setTimeout(() => {
              timeout = null;
              func.apply(self, args);
            }, wait);
          }
        };
      },

      // 上传按钮点击事件
      handleUploadTap() {
        console.log('this.data.isUploading', this.data.isUploading);

        if (this.data.isUploading) {
          wx.showToast({
            title: '请等待上传完毕',
            icon: 'none'
          });
          return;
        }
        if (this.data.allFiles.length >= this.data.maxFiles) {
          wx.showToast({
            title: `最多上传${this.data.maxFiles}个文件`,
            icon: 'none'
          });
          return;
        }
        this.chooseFiles();
      },
      // 选择文件
      chooseFiles() {
        const { acceptTypes } = this.data;
        const actions: string[] = [];
        if (acceptTypes.includes('image') || acceptTypes.includes('video')) {
          actions.push('选择图片或视频');
        }
        if (acceptTypes.includes('file')) {
          actions.push('选择文件');
        }
        wx.showActionSheet({
          itemList: actions,
          success: res => {
            const selectedAction = actions[res.tapIndex];
            this.handleFileSelection(selectedAction);
          }
        });
      },
      // 处理文件选择
      handleFileSelection(action: string) {
        if (action.includes('文件')) {
          this.chooseFile();
        } else if (action.includes('视频') || action.includes('图片')) {
          this.chooseMedia();
        }
      },
      chooseMedia() {
        const remainingCount = this.data.maxFiles - this.data.allFiles.length;
        const acceptTypes = this.data.acceptTypes;
        let mediaType: string[] = [];
        if (acceptTypes.includes('image') && acceptTypes.includes('video')) {
          mediaType = ['image', 'video', 'mix'];
        } else if (acceptTypes.includes('image')) {
          mediaType = ['image'];
        } else if (acceptTypes.includes('video')) {
          mediaType = ['video'];
        }
        wx.chooseMedia({
          count: remainingCount,
          mediaType: mediaType as ('image' | 'video' | 'mix')[],
          success: res => {
            console.log('选择的媒体文件:', res.tempFiles);
            const files = res.tempFiles.map((file, index) => {
              const { size, fileType, tempFilePath, thumbTempFilePath, ...res } =
                file;
              return {
                name: `media_${Date.now()}_${index}`,
                tempUrl: tempFilePath,
                type: this.getFileType(mediaType, tempFilePath, fileType),
                size,
                thumb: thumbTempFilePath,
                status: 'loading',
                percent: 0
              };
            });
            this.checkFile(files) && this.startUpload(files);
          }
        });
      },
      // 选择文件
      chooseFile() {
        const remainingCount = this.data.maxFiles - this.data.allFiles.length;
        wx.chooseMessageFile({
          count: remainingCount,
          type: 'file',
          success: res => {
            console.log('选择的文件:', res.tempFiles);
            const files = res.tempFiles.map(file => {
              const { size, type: fileType, path: tempFilePath, ...res } = file;
              return {
                name: `file_${Date.now()}_${file.name}`,
                tempUrl: tempFilePath,
                type: this.getFileType(
                  this.data.acceptTypes,
                  tempFilePath,
                  fileType
                ),
                size: size,
                status: 'loading',
                percent: 0
              };
            });
            this.checkFile(files) && this.startUpload(files);
          }
        });
      },
      checkFile(files: any[]) {
        const { maxFileSize } = this.data;
        const oversizedFiles = files.filter(
          file => file.size && file.size > maxFileSize * 1024 * 1024
        );
        if (oversizedFiles.length > 0) {
          wx.showToast({
            title: `文件大小不能超过${maxFileSize}MB`,
            icon: 'none'
          });
          return false;
        }
        const videoList = files.filter((file: any) => file.type === 'video');
        if (videoList.length > 1) {
          wx.showToast({
            title: '最多只能上传一个视频',
            icon: 'none'
          });
          return false;
        }
        return true;
      },
      // 批量上传
      async startUpload(files: any[]) {
        this.setData({
          allFiles: [...this.data.allFiles, ...files],
          isUploading: true,
          uploadResult: null
        });
        try {
          const { uploader } = this.data;
          files.forEach(file => uploader.add(file));
          const result = await uploader.complete();
          this.triggerEvent('uploadComplete', {
            successFiles: result.successFiles,
            failedFiles: result.failedFiles
          });
        } catch (error) {
          console.error('上传失败:', error);
          wx.showToast({
            title: '上传失败',
            icon: 'error'
          });
        } finally {
          this.setData({ isUploading: false }, () => {
            console.log('状态已更新 isUploading:', this.data.isUploading);
          });
        }
      },
      // 单个文件上传函数
      async uploadFile(file: any): Promise<any> {
        this.updateFileStatus(file.name, 'loading', 0);
        const tokenData = wx.getStorageSync('JD_AUTH_TOKEN');
        const plugin = requirePlugin('loginPlugin');
        const pt_key = plugin.getPtKey();
        console.log('---开始上传文件0:', JSON.stringify(file));
        try {
          const preUrlRes: any = await new Promise((res, rej) => {
            setTimeout(() => {
              res({
                code: '0000',
                data: {
                  uploadUrl: 'https://mock-oss-upload-url.com/mock-upload',
                  fileKey: file.name + '_mocked'
                }
              });
            }, 2000);
          });
          // const preUrlRes: any = await doRequest({
          //   url: '/infrastructure/oss/getPreUrl',
          //   method: 'POST',
          //   data: {
          //     fileKey: file.name,
          //     bucketName: 'rover-operation'
          //   },
          //   headers: {
          //     'LOP-DN': getLOPDN(),
          //     authType: 2,
          //     Cookie: `pt_key=${pt_key}`,
          //     client: 'm',
          //     appid: 2435,
          //     ticket_type: 'mix'
          //   }
          // });
          console.log('预上传URL响应:', preUrlRes);
          if (preUrlRes.code !== HTTPSTATUSCODE.Success || !preUrlRes.data) {
            throw new Error('获取上传URL失败');
          }
          console.log('---开始上传文件30:', JSON.stringify(file));
          this.updateFileStatus(file.name, 'loading', 30);
          const fileData = await new Promise<ArrayBuffer>((resolve, reject) => {
            wxfs.readFile({
              filePath: file.tempUrl,
              success: (res: any) => {
                console.log('读取文件成功:', file.name);
                resolve(res.data);
              },
              fail: (err: any) => {
                console.error('读取文件失败:', err);
                reject(new Error('读取文件失败'));
              }
            });
          });
          console.log('---开始上传文件50:', JSON.stringify(file));
          this.updateFileStatus(file.name, 'loading', 60);
          const { uploadUrl, fileKey } = preUrlRes.data;
          await new Promise<void>((res, rej) => {
            setTimeout(() => {
              console.log('模拟上传完成:', file.name);
              res();
            }, 2000); // 模拟上传延时
          });
          // await new Promise<void>((resolve, reject) => {
          //   wx.request({
          //     url: uploadUrl,
          //     method: 'PUT',
          //     data: fileData,
          //     header: {
          //       'Content-Type': 'multipart/form-data'
          //     },
          //     success: () => {
          //       console.log('上传成功:', file.name);
          //       resolve();
          //     },
          //     fail: () => reject(new Error('上传失败'))
          //   });
          // });
          console.log('---开始上传文件100:', JSON.stringify(file));
          this.updateFileStatus(file.name, 'done', 100);
          return {
            fileKey,
            type: file.type
          };
        } catch (error) {
          console.error('上传失败:', error);
          this.updateFileStatus(file.name, 'failed', 0);
          throw error;
        }
      },
      updateFileStatus(fileName: string, status: string, percent: number) {
        const { allFiles } = this.data;
        const index = allFiles.findIndex((f: any) => f.name === fileName);
        // 如果是全量更新percent为0，如果是路径更新percent为实际值
        if (index !== -1) {
          this.setData({
            [`allFiles[${index}].status`]: status,
            [`allFiles[${index}].percent`]: percent
          });
        }
      },
      // 获取文件类型
      getFileType(
        mediaType: string[],
        tempFilePath: string,
        fileType?: string
      ): string {
        if (fileType) return fileType;
        if (mediaType.length === 1) {
          return mediaType[0];
        }
        const temp = tempFilePath.split('.');
        const postfix = temp[temp.length - 1].toLocaleLowerCase();
        const typeMap: { [key: string]: string } = {
          jpg: 'image',
          jpeg: 'image',
          png: 'image',
          gif: 'image',
          bmp: 'image',
          webp: 'image',
          mp4: 'video',
          mov: 'video',
          avi: 'video',
          wmv: 'video',
          flv: 'video',
          '3gp': 'video'
        };
        return (postfix && typeMap[postfix]) || postfix || 'file';
      },
      handleDelete(e: any) {
        const { file } = e.detail;
        console.log('handleDelete:', file);
        const newFiles = this.data.allFiles.filter(
          (f: any) => f.name !== file.name
        );
        this.setData({ allFiles: newFiles });
        this.triggerEvent('fileRemove', { file });
      },
      clearAllFiles() {
        this.setData({
          allFiles: [],
          _cachedMediaFiles: [],
          _cachedOtherFiles: [],
          _lastFilesHash: ''
        });
      },
      getMediaFiles(files: any[]) {
        return files.filter(
          (file: any) => file.type === 'image' || file.type === 'video'
        );
      },
      getOtherFiles(files: any[]) {
        return files.filter(
          (file: any) => file.type !== 'image' && file.type !== 'video'
        );
      },
      updateComputedData(newValue: any = {}) {
        let isUploading = this.data.isUploading;
        const { allFiles } = this.data;
        if (newValue.isUploading !== undefined) {
          isUploading = newValue.isUploading;
        }
        const mediaFiles = this.getMediaFiles(allFiles);
        const otherFiles = this.getOtherFiles(allFiles);
        this.triggerEvent('previewInfoChange', {
          mediaCount: mediaFiles.length,
          otherCount: otherFiles.length,
          isUploading
        });
        this.setData({
          isUploading,
          mediaFiles,
          otherFiles
        });
      }
    }
  });
</script>

<style lang="scss">
  .enhanced-uploader {
    display: flex;
    position: relative;
    &.row {
      flex-direction: row;
    }
    &.column {
      flex-direction: column;
    }
    &.column-reverse {
      flex-direction: column-reverse;
    }
  }

  .upload-trigger-container {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: auto;
    height: auto;
    min-height: 72rpx;
    position: relative;
  }

  .default-upload-btn {
    width: 100%;
    height: 160rpx;
    border: 4rpx dashed #ccc;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f8f8;

    .upload-text {
      color: #666;
      font-size: 56rpx;
    }
  }
</style>

<script name="json">
  module.exports = {
    component: true,
    usingComponents: {
      'preview-media': 'shared/ui/previewMedia.mpx',
      'preview-files': 'shared/ui/previewFiles.mpx'
    }
  };
</script>
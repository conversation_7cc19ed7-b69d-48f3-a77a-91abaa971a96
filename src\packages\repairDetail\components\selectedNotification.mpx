<template>
  <block wx:if="{{visible}}">
    <!-- 遮罩层 -->
    <view
      class="selected-notification-overlay {{animationClass}}"
      style="height: calc(100vh - {{bottomBarHeight}}rpx);"
      bindtap="onVisibleChange"
    ></view>

    <!-- 内容容器 -->
    <view
      class="selected-notification-container {{animationClass}}"
      style="bottom: {{bottomBarHeight}}rpx;"
    >
      <view class="selected-notification">
        <view class="header">
          <view class="title">已选择的收件人</view>
          <view bindtap="onVisibleChange" class="close">
            <image src="{{TechMaintainUrl.ClosePopup}}" class="close-icon" />
          </view>
        </view>

        <scroll-view
          scroll-y
          type="list"
          show-scrollbar="{{true}}"
          class="selected-list"
        >
          <view wx:if="{{selectedUsers.length === 0}}" class="empty-state">
            暂无选择的收件人
          </view>

          <view wx:else>
            <view
              wx:for="{{selectedUsers}}"
              wx:key="userName"
              class="user-item"
            >
              <view class="user-info">
                <text class="user-name">{{ item.nameAndContactInfo }}</text>
              </view>
              <view
                class="delete-btn"
                bindtap="handleDeleteUser"
                data-user="{{item}}"
              >
                <image src="{{TechMaintainUrl.Delete}}" class="delete-icon" />
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </block>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { TechMaintainUrl } from 'shared/assets/imageUrl';
  import TechMaintenanceApi from 'shared/api/techMaintenance';
  import { addGlobalEventListener } from 'shared/utils/emit';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';

  createComponent({
    properties: {
      visible: {
        type: Boolean,
        value: false
      },
      selectedUsers: {
        type: Array,
        value: []
      },
      bottomBarHeight: {
        type: Number,
        value: 120 // 底部按钮区域的高度，单位rpx
      }
    },
    data: {
      techMaintenanceApi: new TechMaintenanceApi(),
      TechMaintainUrl,
      animationClass: '' // 控制动画状态
    },
    lifetimes: {
      created() {
        addGlobalEventListener('getRepairOrderNumber', orderNumber => {
          this.orderNumber = orderNumber;
        });
      },
      attached() {},
      detached() {},
      ready() {}
    },
    watch: {
      visible(newVal: boolean) {
        if (newVal) {
          // 显示时添加进入动画
          this.setData({
            animationClass: 'slide-in'
          });
        } else {
          // 隐藏时添加退出动画
          this.setData({
            animationClass: 'slide-out'
          });
        }
      }
    },
    methods: {
      onVisibleChange() {
        console.log('关闭弹窗');
        // 先播放退出动画
        this.setData({
          animationClass: 'slide-out'
        });

        // 动画完成后触发关闭事件
        setTimeout(() => {
          this.triggerEvent('close');
        }, 300); // 与CSS动画时间保持一致
      },

      handleDeleteUser(event: any) {
        const user = event.currentTarget.dataset.user;
        console.log('删除用户:', user);

        // 触发删除事件，传递用户信息给父组件
        this.triggerEvent('deleteUser', { user });
      }
    }
  });
</script>

<style lang="scss">
  .selected-notification-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    background: rgba(0, 0, 0, 0.5);
    z-index: 12000; // 比t-popup的默认11000更高
    opacity: 0;
    transition: opacity 0.3s ease-in-out;

    &.slide-in {
      opacity: 1;
    }

    &.slide-out {
      opacity: 0;
    }
  }

  .selected-notification-container {
    position: fixed;
    left: 0;
    width: 100vw;
    background: #ffffff;
    z-index: 12001; // 比遮罩层更高
    border-top-left-radius: 16rpx;
    border-top-right-radius: 16rpx;
    transform: translateY(100%);
    transition: all 0.3s ease-in-out;

    &.slide-in {
      transform: translateY(0);
    }

    &.slide-out {
      transform: translateY(100%);
    }
  }

  .selected-notification {
    padding: 0 32rpx;
    max-height: 60vh;

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 100rpx;

      .title {
        font-weight: 600;
        font-size: 32rpx;
        color: #333;
      }

      .close {
        width: 32rpx;
        height: 32rpx;

        .close-icon {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
    }

    .selected-list {
      max-height: calc(60vh - 100rpx);

      .empty-state {
        text-align: center;
        color: #999;
        font-size: 28rpx;
        padding: 100rpx 0;
      }

      .user-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 24rpx 0;
        border-bottom: 1px solid #f5f5f5;

        &:last-child {
          border-bottom: none;
        }

        .user-info {
          flex: 1;

          .user-name {
            font-size: 30rpx;
            color: #333;
            line-height: 1.4;
          }
        }

        .delete-btn {
          width: 48rpx;
          height: 48rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          .delete-icon {
            width: 32rpx;
            height: 32rpx;
            object-fit: contain;
          }
        }
      }
    }
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {}
  }
</script>

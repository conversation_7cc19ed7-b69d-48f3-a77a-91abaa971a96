<template>
  <view class="operate-log-detail-container">
    <!-- 导航栏 -->
    <navi-bar
      title="维修单详情"
      showReturn="{{true}}"
      backUrl="{{backUrl}}"
      showAddress="{{false}}"
      backgroundColor="transparent"
    ></navi-bar>
  </view>
</template>

<script lang="ts">
  import { createPage } from '@mpxjs/core';
  import TechMaintenanceApi from 'shared/api/techMaintenance';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';
  import { TechMaintainUrl } from 'shared/assets/imageUrl';
  import { sendGlobalEvent } from 'shared/utils/emit';

  createPage<any>({
    data: {
      fetchApi: new TechMaintenanceApi(),
      backUrl: '/pages/techMaintenance/index',
      TechMaintainUrl
    },
    computed: {},
    onLoad(options: { number?: string }) {
      wx.setNavigationBarColor({
        frontColor: '#000000',
        backgroundColor: '#ffffff'
      });

      if (options.number) {
        this.setData({
          orderNumber: options.number
        });
      } else {
        console.error('没有收到维修单号参数');
      }
    },
    onReady() {},
    methods: {}
  });
</script>

<style lang="scss" scoped>
  page {
    height: 100%;
    width: 100%;
    overflow: hidden;
    background: url('https://storage.360buyimg.com/jdx-autopilot-ota/k2-minimonitor/techMaintain/bg-maintaining.png?Expires=3899341225&AccessKey=n828WHAXD584pTvi&Signature=BVSz3Nggtxxx9zCwYZKLIzS%2BTDQ%3D')
      no-repeat center;
    background-size: cover;
  }

  .operate-log-detail-container {
    height: 100%;
    width: 100%;
    position: relative;
    box-sizing: border-box;
  }
</style>

<script type="application/json">
  {
    "usingComponents": {
      "navi-bar": "shared/ui/naviBar.mpx"
    },
    "navigationStyle": "custom",
    "renderer": "webview"
  }
</script>
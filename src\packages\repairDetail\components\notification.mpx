<template>
  <t-popup
    visible="{{visible}}"
    bind:visible-change="onVisibleChange"
    placement="bottom"
  >
    <view class="notification">
      <view class="block">
        <view class="header">
          <view class="title">选择收件人</view>
          <view bindtap="onVisibleChange" class="close">
            <image src="{{TechMaintainUrl.ClosePopup}}" class="close-icon" />
          </view>
        </view>
      </view>

      <!-- Tab导航 -->
      <view class="tab-container">
        <t-tabs
          class="notification-tabs"
          value="{{activeTabKey}}"
          space-evenly="{{true}}"
          bind:change="onTabsChange"
        >
          <t-tab-panel
            wx:for="{{tabsList}}"
            wx:key="key"
            label="{{item.name}}"
            value="{{item.key}}"
          />
        </t-tabs>
      </view>
      <!-- Tab内容区域 -->
      <view class="tab-content-wrapper">
        <!-- 组织名称 -->
        <view class="org-name" wx:if="{{activeTabKey === 'OEM'}}">{{
          oemName
        }}</view>
        <view
          class="org-name"
          wx:elif="{{activeTabKey === 'PROGRAM_SERVICE_STATION'}}"
          >{{ serviceStationName }}</view
        >

        <!-- 滚动内容区域 -->
        <scroll-view
          scroll-y
          type="list"
          show-scrollbar="{{true}}"
          class="tab-content"
        >
          <!-- 车厂内容 -->
          <view wx:if="{{activeTabKey === 'OEM'}}" class="content-section">
            <t-checkbox-group
              value="{{selectedOemUsers}}"
              bind:change="handleOemUsersChange"
              options="{{oemUserOptions}}"
              placement="right"
            />
          </view>
          <!-- 京东受理人内容 -->
          <view wx:elif="{{activeTabKey === 'JD'}}" class="content-section">
            <t-checkbox-group
              value="{{selectedJdUsers}}"
              bind:change="handleJdUsersChange"
              options="{{jdUserOptions}}"
              placement="right"
            />
          </view>
          <!-- 服务站内容 -->
          <view
            wx:elif="{{activeTabKey === 'PROGRAM_SERVICE_STATION'}}"
            class="content-section"
          >
            <t-checkbox-group
              value="{{selectedStationUsers}}"
              bind:change="handleStationUsersChange"
              options="{{stationUserOptions}}"
              placement="right"
            />
          </view>
        </scroll-view>
      </view>

      <!-- 底部按钮区域 -->
      <view class="bottom-actions">
        <view
          class="selected-count {{selectedUsersVisible ? 'active' : ''}}"
          bindtap="handleShowSelectedUsers"
        >
          <text class="count-text">已选择（{{ totalSelectedCount }}）</text>
          <view
            class="arrow {{selectedUsersVisible ? 'arrow-up' : 'arrow-down'}}"
          ></view>
        </view>
        <view class="send-btn expanded-btn" bindtap="handleSendNotification">
          发送邮件通知
        </view>
      </view>
    </view>
  </t-popup>

  <!-- 已选择用户弹窗 -->
  <selected-notification
    visible="{{selectedUsersVisible}}"
    selectedUsers="{{allSelectedUsers}}"
    bottomBarHeight="{{120}}"
    bind:close="handleCloseSelectedUsers"
    bind:deleteUser="handleDeleteSelectedUser"
  />
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { TechMaintainUrl } from 'shared/assets/imageUrl';
  import TechMaintenanceApi from 'shared/api/techMaintenance';
  import { addGlobalEventListener } from 'shared/utils/emit';
  import { HTTPSTATUSCODE } from 'shared/api/fetch';

  createComponent({
    properties: {
      visible: {
        type: Boolean,
        value: false
      }
    },
    data: {
      techMaintenanceApi: new TechMaintenanceApi(),
      TechMaintainUrl,
      activeTabKey: 'OEM', // 默认显示车厂tab
      tabs: {
        OEM: { key: 'OEM', name: '车厂' },
        JD: { key: 'JD', name: '京东受理人' },
        PROGRAM_SERVICE_STATION: {
          key: 'PROGRAM_SERVICE_STATION',
          name: '服务站'
        }
      },
      // 组织名称
      oemName: '',
      serviceStationName: '',
      // 用户列表选项
      oemUserOptions: [] as any[],
      jdUserOptions: [] as any[],
      stationUserOptions: [] as any[],
      // 用户列表缓存map
      userListCache: new Map() as Map<string, any[]>,
      // 选中的用户
      selectedOemUsers: [] as string[],
      selectedJdUsers: [] as string[],
      selectedStationUsers: [] as string[],
      // 已选择用户弹窗
      selectedUsersVisible: false
    },
    computed: {
      tabsList() {
        return Object.values(this.tabs);
      },
      totalSelectedCount() {
        return (
          this.selectedOemUsers.length +
          this.selectedJdUsers.length +
          this.selectedStationUsers.length
        );
      },
      allSelectedUsers() {
        const users: any[] = [];
        // 添加车厂用户
        this.selectedOemUsers.forEach(userName => {
          const user = this.oemUserOptions.find(
            option => option.value === userName
          );
          if (user) {
            users.push({
              userName: user.value,
              nameAndContactInfo: user.label,
              type: 'OEM'
            });
          }
        });
        // 添加京东用户
        this.selectedJdUsers.forEach(userName => {
          const user = this.jdUserOptions.find(
            option => option.value === userName
          );
          if (user) {
            users.push({
              userName: user.value,
              nameAndContactInfo: user.label,
              type: 'JD'
            });
          }
        });
        // 添加服务站用户
        this.selectedStationUsers.forEach(userName => {
          const user = this.stationUserOptions.find(
            option => option.value === userName
          );
          if (user) {
            users.push({
              userName: user.value,
              nameAndContactInfo: user.label,
              type: 'PROGRAM_SERVICE_STATION'
            });
          }
        });

        return users;
      }
    },
    lifetimes: {
      created() {
        addGlobalEventListener('getRepairOrderNumber', orderNumber => {
          this.orderNumber = orderNumber;
        });
      },
      attached() {
        this.initData();
      },
      detached() {},
      ready() {}
    },

    methods: {
      onVisibleChange() {
        console.log('关闭弹窗');
        this.triggerEvent('close');
      },

      async onTabsChange(event: any) {
        const { value } = event.detail;
        this.setData({
          activeTabKey: value
        });
        console.log('切换到tab:', value);

        // 切换tab时加载对应的用户列表
        await this.loadUserList(
          value as 'JD' | 'OEM' | 'PROGRAM_SERVICE_STATION'
        );
      },

      async initData() {
        // 获取组织名称信息
        await this.getOrganizationInfo();
        // 加载默认tab的用户列表
        await this.loadUserList(
          this.activeTabKey as 'JD' | 'OEM' | 'PROGRAM_SERVICE_STATION'
        );
      },

      async getOrganizationInfo() {
        try {
          const res = await this.techMaintenanceApi.getVisibleDetails(
            this.orderNumber
          );
          if (res.code === HTTPSTATUSCODE.Success) {
            this.setData({
              oemName: res.data.oemName || '',
              serviceStationName: res.data.serviceStationName || ''
            });
          }
        } catch (error) {
          console.error('获取组织信息失败:', error);
        }
      },

      async loadUserList(type: 'JD' | 'OEM' | 'PROGRAM_SERVICE_STATION') {
        // 先检查缓存
        if (this.userListCache.has(type)) {
          console.log(`从缓存加载${type}用户列表`);
          const cachedOptions = this.userListCache.get(type);
          if (cachedOptions) {
            this.setUserOptions(type, cachedOptions);
            return;
          }
        }

        try {
          console.log(`从接口加载${type}用户列表`);
          const res = await this.techMaintenanceApi.getNotifyUserList({
            number: this.orderNumber,
            type
          });

          if (res.code === HTTPSTATUSCODE.Success && res.data) {
            const options = res.data.map((user: any) => ({
              label: `${user.realName}(${user.erp || user.email})`,
              value: user.userName
            }));

            // 存入缓存
            this.userListCache.set(type, options);

            // 设置到对应的数据
            this.setUserOptions(type, options);
          }
        } catch (error) {
          console.error('获取用户列表失败:', error);
          wx.showToast({
            title: '获取用户列表失败',
            icon: 'none'
          });
        }
      },

      // 设置用户选项到对应的数据字段
      setUserOptions(
        type: 'JD' | 'OEM' | 'PROGRAM_SERVICE_STATION',
        options: any[]
      ) {
        switch (type) {
          case 'OEM':
            this.setData({ oemUserOptions: options });
            break;
          case 'JD':
            this.setData({ jdUserOptions: options });
            break;
          case 'PROGRAM_SERVICE_STATION':
            this.setData({ stationUserOptions: options });
            break;
        }
      },

      handleOemUsersChange(event: any) {
        this.setData({
          selectedOemUsers: event.detail.value
        });
      },

      handleJdUsersChange(event: any) {
        this.setData({
          selectedJdUsers: event.detail.value
        });
      },

      handleStationUsersChange(event: any) {
        this.setData({
          selectedStationUsers: event.detail.value
        });
      },

      // 发送通知
      async handleSendNotification() {
        if (this.totalSelectedCount === 0) {
          wx.showToast({
            title: '请至少选择一个收件人',
            icon: 'none'
          });
          return;
        }
        try {
          wx.showLoading({ title: '发送中...' });
          const params = {
            number: this.orderNumber,
            oemUserNameList: this.selectedOemUsers,
            serviceStationUserNameList: this.selectedStationUsers,
            vehicleReportUserName:
              this.selectedJdUsers.length > 0 ? this.selectedJdUsers[0] : ''
          };
          console.log('发送通知参数:', params);
          const res = await this.techMaintenanceApi.sendNotify(params);
          if (res.code === HTTPSTATUSCODE.Success) {
            wx.showToast({
              title: '发送成功',
              icon: 'success'
            });
            this.triggerEvent('close');
          } else {
            wx.showToast({
              title: res.message || '发送失败',
              icon: 'none'
            });
          }
        } catch (error) {
          console.error('发送通知失败:', error);
          wx.showToast({
            title: '发送失败',
            icon: 'none'
          });
        } finally {
          wx.hideLoading();
        }
      },

      // 切换已选择用户弹窗显示状态
      handleShowSelectedUsers() {
        if (this.totalSelectedCount === 0) {
          wx.showToast({
            title: '暂无选择的收件人',
            icon: 'none'
          });
          return;
        }
        this.setData({
          selectedUsersVisible: !this.selectedUsersVisible
        });
      },

      // 关闭已选择用户弹窗
      handleCloseSelectedUsers() {
        this.setData({
          selectedUsersVisible: false
        });
      },

      // 删除选中的用户
      handleDeleteSelectedUser(event: any) {
        const { user } = event.detail;
        switch (user.type) {
          case 'OEM':
            this.setData({
              selectedOemUsers: this.selectedOemUsers.filter(
                userName => userName !== user.userName
              )
            });
            break;
          case 'JD':
            this.setData({
              selectedJdUsers: this.selectedJdUsers.filter(
                userName => userName !== user.userName
              )
            });
            break;
          case 'PROGRAM_SERVICE_STATION':
            this.setData({
              selectedStationUsers: this.selectedStationUsers.filter(
                userName => userName !== user.userName
              )
            });
            break;
        }
        wx.showToast({
          title: '已移除',
          icon: 'success',
          duration: 1000
        });
      }
    }
  });
</script>

<style lang="scss" scoped>
  .notification {
    padding: 0 32rpx;
    height: 80vh;
    max-height: 1000rpx;

    .block {
      width: 100vw;
      .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 100rpx;
      }
      .title {
        text-align: center;
        font-weight: 600;
        font-size: 32rpx;
      }
      .close {
        width: 32rpx;
        height: 32rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.1); // 临时调试背景
        .close-icon {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
    }

    .tab-container {
      margin-top: 20rpx;
      .notification-tabs {
        border-bottom: 1px solid #f0f0f0;
      }
    }

    .tab-content-wrapper {
      flex: 1;
      height: calc(80vh - 280rpx);
      max-height: 720rpx;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;

      .org-name {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        padding: 32rpx 16rpx 24rpx 16rpx;
        flex-shrink: 0; // 防止被压缩
      }

      .tab-content {
        flex: 1;
        height: 0; // 关键：设置为0让flex生效
        padding: 0 16rpx;

        .content-section {
          padding: 0;
        }

        .placeholder {
          text-align: center;
          color: #999;
          font-size: 28rpx;
          padding: 100rpx 0;
        }
      }
    }

    .bottom-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 24rpx 0;

      .selected-count {
        display: flex;
        align-items: center;
        font-size: 28rpx;
        color: #666;
        cursor: pointer;
        transition: color 0.3s ease;

        &.active {
          color: #fa3619;

          .arrow {
            border-top-color: #fa3619;
            border-left-color: #fa3619;
          }
        }

        .count-text {
          margin-right: 8rpx;
        }

        .arrow {
          width: 12rpx;
          height: 12rpx;
          border-top: 2rpx solid #666;
          border-left: 2rpx solid #666;
          transition:
            transform 0.3s ease,
            border-color 0.3s ease;

          &.arrow-down {
            transform: rotate(-135deg);
          }

          &.arrow-up {
            transform: rotate(45deg);
          }
        }
      }

      .expanded-btn {
        padding: 12rpx 24rpx;
        border-radius: 32rpx;
        font-size: 32rpx;

        &.send-btn {
          background: #fa3619;
          border: 2rpx solid #fff;
          color: #ffffff;
          font-weight: 500;
        }
      }
    }
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "t-popup": "tdesign-miniprogram/popup/popup",
      "t-tabs": "tdesign-miniprogram/tabs/tabs",
      "t-tab-panel": "tdesign-miniprogram/tab-panel/tab-panel",
      "t-checkbox-group": "tdesign-miniprogram/checkbox-group/checkbox-group",
      "selected-notification": "./selectedNotification"
    }
  }
</script>
